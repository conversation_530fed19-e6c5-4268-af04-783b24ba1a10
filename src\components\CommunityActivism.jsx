import React, { useState, useEffect } from 'react';
import { useAuth } from '../AuthContext';
import { collection, addDoc, query, orderBy, onSnapshot, doc, updateDoc, arrayUnion, arrayRemove } from 'firebase/firestore';
import { db } from '../firebase';

const ACTIVISM_CATEGORIES = [
  {
    id: 'voting_rights',
    title: 'Voting & Civic Engagement',
    description: 'Voter registration, election information, civic participation',
    icon: '🗳️',
    color: '#3742FA'
  },
  {
    id: 'economic_justice',
    title: 'Economic Justice',
    description: 'Fair wages, business support, economic equality',
    icon: '💰',
    color: '#2ED573'
  },
  {
    id: 'education_equity',
    title: 'Education Equity',
    description: 'School funding, educational access, student support',
    icon: '📚',
    color: '#FFA502'
  },
  {
    id: 'criminal_justice',
    title: 'Criminal Justice Reform',
    description: 'Police accountability, prison reform, legal justice',
    icon: '⚖️',
    color: '#FF4757'
  },
  {
    id: 'healthcare_access',
    title: 'Healthcare Access',
    description: 'Medical equity, mental health, community wellness',
    icon: '🏥',
    color: '#5F27CD'
  },
  {
    id: 'community_development',
    title: 'Community Development',
    description: 'Housing, infrastructure, neighborhood improvement',
    icon: '🏘️',
    color: '#00D2D3'
  }
];

const ACTION_TYPES = [
  { id: 'petition', label: 'Sign Petition', icon: '📝' },
  { id: 'event', label: 'Attend Event', icon: '📅' },
  { id: 'volunteer', label: 'Volunteer', icon: '🤝' },
  { id: 'donate', label: 'Donate', icon: '💝' },
  { id: 'contact', label: 'Contact Officials', icon: '📞' },
  { id: 'share', label: 'Share Awareness', icon: '📢' }
];

export default function CommunityActivism() {
  const { currentUser } = useAuth();
  const [campaigns, setCampaigns] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newCampaign, setNewCampaign] = useState({
    title: '',
    description: '',
    category: '',
    actionType: '',
    targetDate: '',
    location: '',
    externalLink: '',
    goal: ''
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadCampaigns();
  }, []);

  const loadCampaigns = () => {
    const campaignsRef = collection(db, 'activismCampaigns');
    const q = query(campaignsRef, orderBy('createdAt', 'desc'));

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const campaignData = snapshot.docs.map(doc => ({ 
        id: doc.id, 
        ...doc.data() 
      }));
      setCampaigns(campaignData);
    });

    return unsubscribe;
  };

  const createCampaign = async () => {
    if (!currentUser || !newCampaign.title.trim() || !newCampaign.category) return;

    setLoading(true);
    try {
      await addDoc(collection(db, 'activismCampaigns'), {
        ...newCampaign,
        creatorId: currentUser.uid,
        creatorName: currentUser.displayName || currentUser.email,
        supporters: [],
        participantCount: 0,
        status: 'active',
        createdAt: new Date().toISOString(),
        lastActivity: new Date().toISOString()
      });

      setNewCampaign({
        title: '',
        description: '',
        category: '',
        actionType: '',
        targetDate: '',
        location: '',
        externalLink: '',
        goal: ''
      });
      setShowCreateForm(false);
    } catch (error) {
      console.error('Error creating campaign:', error);
    }
    setLoading(false);
  };

  const joinCampaign = async (campaignId) => {
    if (!currentUser) return;

    try {
      const campaignRef = doc(db, 'activismCampaigns', campaignId);
      await updateDoc(campaignRef, {
        supporters: arrayUnion(currentUser.uid),
        participantCount: campaigns.find(c => c.id === campaignId)?.participantCount + 1 || 1,
        lastActivity: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error joining campaign:', error);
    }
  };

  const leaveCampaign = async (campaignId) => {
    if (!currentUser) return;

    try {
      const campaignRef = doc(db, 'activismCampaigns', campaignId);
      const campaign = campaigns.find(c => c.id === campaignId);
      await updateDoc(campaignRef, {
        supporters: arrayRemove(currentUser.uid),
        participantCount: Math.max(0, (campaign?.participantCount || 1) - 1),
        lastActivity: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error leaving campaign:', error);
    }
  };

  const getFilteredCampaigns = () => {
    if (!selectedCategory) return campaigns;
    return campaigns.filter(campaign => campaign.category === selectedCategory);
  };

  const generateSocialShareText = (campaign) => {
    const actionType = ACTION_TYPES.find(a => a.id === campaign.actionType);
    return `Join me in supporting: "${campaign.title}" 
    
${campaign.description.substring(0, 100)}...

Take action: ${actionType?.label || 'Get involved'}
#BlackCommunity #CommunityAction #NAROOP

${window.location.href}`;
  };

  const shareCampaign = (campaign) => {
    const shareText = generateSocialShareText(campaign);
    
    if (navigator.share) {
      navigator.share({
        title: campaign.title,
        text: shareText,
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(shareText);
      alert('Campaign details copied to clipboard! Share on your social media.');
    }
  };

  return (
    <div className="community-activism">
      <div className="activism-header">
        <h2>✊🏾 Community Activism Hub</h2>
        <p>Organize, mobilize, and create change together</p>
      </div>

      <div className="activism-stats">
        <div className="stat-card">
          <div className="stat-number">{campaigns.length}</div>
          <div className="stat-label">Active Campaigns</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">
            {campaigns.reduce((sum, c) => sum + (c.participantCount || 0), 0)}
          </div>
          <div className="stat-label">Total Participants</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">
            {campaigns.filter(c => c.supporters?.includes(currentUser?.uid)).length}
          </div>
          <div className="stat-label">Your Campaigns</div>
        </div>
      </div>

      <div className="category-filter">
        <h3>Filter by Category</h3>
        <div className="category-buttons">
          <button 
            className={`category-btn ${!selectedCategory ? 'active' : ''}`}
            onClick={() => setSelectedCategory(null)}
          >
            All Categories
          </button>
          {ACTIVISM_CATEGORIES.map(category => (
            <button
              key={category.id}
              className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
              style={{ borderColor: category.color }}
              onClick={() => setSelectedCategory(category.id)}
            >
              {category.icon} {category.title}
            </button>
          ))}
        </div>
      </div>

      <div className="campaigns-section">
        <div className="campaigns-header">
          <h3>
            {selectedCategory 
              ? `${ACTIVISM_CATEGORIES.find(c => c.id === selectedCategory)?.title} Campaigns`
              : 'All Campaigns'
            } ({getFilteredCampaigns().length})
          </h3>
          <button 
            className="create-campaign-btn"
            onClick={() => setShowCreateForm(true)}
          >
            + Start Campaign
          </button>
        </div>

        {showCreateForm && (
          <div className="campaign-form">
            <h4>✊ Start a New Campaign</h4>

            <div className="form-field">
              <label className="form-label" htmlFor="campaign-title">
                Campaign Title *
              </label>
              <input
                id="campaign-title"
                type="text"
                value={newCampaign.title}
                onChange={(e) => setNewCampaign({...newCampaign, title: e.target.value})}
                placeholder="Create a compelling title for your campaign"
                className="form-input"
                maxLength={100}
                required
              />
              <div className="character-count">{newCampaign.title.length}/100</div>
            </div>

            <div className="form-field">
              <label className="form-label" htmlFor="campaign-description">
                Campaign Description *
              </label>
              <textarea
                id="campaign-description"
                value={newCampaign.description}
                onChange={(e) => setNewCampaign({...newCampaign, description: e.target.value})}
                placeholder="Describe the issue, why it matters, and what specific action you want people to take. Be clear and inspiring!"
                className="form-textarea"
                maxLength={500}
                required
              />
              <div className="character-count">{newCampaign.description.length}/500</div>
            </div>

            <div className="form-field">
              <label className="form-label" htmlFor="campaign-category">
                Campaign Category *
              </label>
              <select
                id="campaign-category"
                value={newCampaign.category}
                onChange={(e) => setNewCampaign({...newCampaign, category: e.target.value})}
                className="form-select"
                required
              >
                <option value="">Choose the category that best fits your campaign</option>
                {ACTIVISM_CATEGORIES.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.title}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-field">
              <label className="form-label" htmlFor="action-type">
                Action Type
              </label>
              <select
                id="action-type"
                value={newCampaign.actionType}
                onChange={(e) => setNewCampaign({...newCampaign, actionType: e.target.value})}
                className="form-select"
              >
                <option value="">Select the type of action you're organizing</option>
                {ACTION_TYPES.map(action => (
                  <option key={action.id} value={action.id}>
                    {action.icon} {action.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-field">
              <label className="form-label" htmlFor="target-date">
                Target Date
              </label>
              <input
                id="target-date"
                type="date"
                value={newCampaign.targetDate}
                onChange={(e) => setNewCampaign({...newCampaign, targetDate: e.target.value})}
                className="form-input"
                min={new Date().toISOString().split('T')[0]}
              />
              <div className="form-helper-text">
                When do you want to achieve your campaign goal?
              </div>
            </div>

            <div className="form-field">
              <label className="form-label" htmlFor="campaign-location">
                Location (Optional)
              </label>
              <input
                id="campaign-location"
                type="text"
                value={newCampaign.location}
                onChange={(e) => setNewCampaign({...newCampaign, location: e.target.value})}
                placeholder="City, state, or specific venue if applicable"
                className="form-input"
                maxLength={100}
              />
            </div>

            <div className="form-field">
              <label className="form-label" htmlFor="external-link">
                External Link (Optional)
              </label>
              <input
                id="external-link"
                type="url"
                value={newCampaign.externalLink}
                onChange={(e) => setNewCampaign({...newCampaign, externalLink: e.target.value})}
                placeholder="https://example.com/petition-or-event-page"
                className="form-input"
              />
              <div className="form-helper-text">
                Link to petition, event page, or other relevant resource
              </div>
            </div>

            <div className="form-field">
              <label className="form-label" htmlFor="campaign-goal">
                Campaign Goal
              </label>
              <input
                id="campaign-goal"
                type="text"
                value={newCampaign.goal}
                onChange={(e) => setNewCampaign({...newCampaign, goal: e.target.value})}
                placeholder="e.g., '1000 signatures', '100 volunteers', '$5000 raised'"
                className="form-input"
                maxLength={50}
              />
              <div className="form-helper-text">
                What specific, measurable outcome are you working toward?
              </div>
            </div>

            <div className="form-actions">
              <button
                onClick={createCampaign}
                disabled={loading || !newCampaign.title.trim() || !newCampaign.category}
                className={`submit-btn ${loading ? 'loading' : ''}`}
              >
                {loading ? 'Creating...' : '🚀 Launch Campaign'}
              </button>
              <button
                onClick={() => setShowCreateForm(false)}
                className="cancel-btn"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        <div className="campaigns-list">
          {getFilteredCampaigns().length === 0 ? (
            <div className="no-campaigns">
              <p>No campaigns in this category yet. Start one to mobilize your community!</p>
            </div>
          ) : (
            getFilteredCampaigns().map(campaign => {
              const category = ACTIVISM_CATEGORIES.find(c => c.id === campaign.category);
              const actionType = ACTION_TYPES.find(a => a.id === campaign.actionType);
              const isSupporter = campaign.supporters?.includes(currentUser?.uid);
              const isCreator = campaign.creatorId === currentUser?.uid;

              return (
                <div key={campaign.id} className="campaign-card">
                  <div className="campaign-header">
                    <div className="campaign-category" style={{ backgroundColor: category?.color }}>
                      {category?.icon} {category?.title}
                    </div>
                    <div className="campaign-action-type">
                      {actionType?.icon} {actionType?.label}
                    </div>
                  </div>

                  <h4>{campaign.title}</h4>
                  <p className="campaign-description">{campaign.description}</p>

                  <div className="campaign-details">
                    {campaign.goal && (
                      <div className="campaign-goal">
                        <strong>Goal:</strong> {campaign.goal}
                      </div>
                    )}
                    {campaign.targetDate && (
                      <div className="campaign-date">
                        <strong>Target Date:</strong> {new Date(campaign.targetDate).toLocaleDateString()}
                      </div>
                    )}
                    {campaign.location && (
                      <div className="campaign-location">
                        <strong>Location:</strong> {campaign.location}
                      </div>
                    )}
                  </div>

                  <div className="campaign-stats">
                    <span>👥 {campaign.participantCount || 0} supporters</span>
                    <span>👤 Created by {campaign.creatorName}</span>
                    <span>📅 {new Date(campaign.createdAt).toLocaleDateString()}</span>
                  </div>

                  <div className="campaign-actions">
                    {!isCreator && (
                      <button 
                        onClick={() => isSupporter ? leaveCampaign(campaign.id) : joinCampaign(campaign.id)}
                        className={`support-btn ${isSupporter ? 'supported' : ''}`}
                      >
                        {isSupporter ? '✓ Supported' : '+ Support'}
                      </button>
                    )}
                    
                    {campaign.externalLink && (
                      <a 
                        href={campaign.externalLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="external-link-btn"
                      >
                        Take Action →
                      </a>
                    )}
                    
                    <button 
                      onClick={() => shareCampaign(campaign)}
                      className="share-btn"
                    >
                      📢 Share
                    </button>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>

      <div className="activism-resources">
        <h3>🔗 Activism Resources</h3>
        <div className="resources-grid">
          <a href="https://www.vote.org/" target="_blank" rel="noopener noreferrer" className="resource-link">
            Voter Registration
          </a>
          <a href="https://www.congress.gov/" target="_blank" rel="noopener noreferrer" className="resource-link">
            Contact Congress
          </a>
          <a href="https://www.change.org/" target="_blank" rel="noopener noreferrer" className="resource-link">
            Start a Petition
          </a>
          <a href="https://www.aclu.org/" target="_blank" rel="noopener noreferrer" className="resource-link">
            Know Your Rights
          </a>
        </div>
      </div>
    </div>
  );
}
